%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: ramp50
  serializedVersion: 8
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 72
    topology: 0
    firstVertex: 0
    vertexCount: 36
    localAABB:
      m_Center: {x: 6.0890102, y: 0.8205422, z: -2.000001}
      m_Extent: {x: 3.9109895, y: 0.17945778, z: 1.000001}
  - serializedVersion: 2
    firstByte: 144
    indexCount: 12
    topology: 0
    firstVertex: 36
    vertexCount: 8
    localAABB:
      m_Center: {x: 6.0890102, y: 0.9999995, z: -2.0000012}
      m_Extent: {x: 3.9109895, y: 0.0000005066395, z: 1.0000005}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexBuffer: 000001000200010003000200040005000600050007000600080009000a000b000a0009000c000d000e000e000f000c001000110012001300120011000a000b001400150014000b00140015001600170016001500180019001a001b001a0019001c000c000f000f001d001c001e001c001d001d001f001e00200010001200120021002000220020002100210023002200240025002600270026002500280029002a002b002a002900
  m_Skin: []
  m_VertexData:
    m_CurrentChannels: 159
    m_VertexCount: 44
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 40
      format: 0
      dimension: 2
    - stream: 0
      offset: 48
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 56
      format: 0
      dimension: 4
    m_DataSize: 3168
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 6.0890102, y: 0.8205422, z: -2.000001}
    m_Extent: {x: 3.9109895, y: 0.17945778, z: 1.000001}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshOptimized: 0
