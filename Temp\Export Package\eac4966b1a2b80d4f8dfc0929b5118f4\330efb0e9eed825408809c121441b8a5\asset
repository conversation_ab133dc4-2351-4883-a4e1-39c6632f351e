%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2700874171910072504
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2700874171910072505}
  - component: {fileID: 2700874171910072509}
  - component: {fileID: 2700874171910072506}
  - component: {fileID: 3851937974465073233}
  - component: {fileID: 2700874171910072507}
  m_Layer: 0
  m_Name: vThirdPersonCamera_LITE
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2700874171910072505
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700874171910072504}
  m_LocalRotation: {x: 0.0013653951, y: -0.9818259, z: 0.18964753, w: 0.0070688}
  m_LocalPosition: {x: 25.086567, y: 2.8048506, z: 16.291666}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 21.865002, y: -179.175, z: 0}
--- !u!20 &2700874171910072509
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700874171910072504}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_GateFitMode: 2
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.03
  far clip plane: 300
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!81 &2700874171910072506
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700874171910072504}
  m_Enabled: 1
--- !u!114 &3851937974465073233
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700874171910072504}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 19c789670a80b704eba4dfebed94a111, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  header: About the Camera System
  comment: "In the Full Version you will get: \n\n- CameraStates: You can create different
    CameraStates for each states of your character, Aiming, Crouching, etc..\n- CameraModes:
    You can set a Fixed Camera, Fixed that look at the Target, Fixed Angles, Triggers
    to ChangeStates\n- Much more options like offsets, angle limitation, smoothness, "
  inEdit: 0
--- !u!114 &2700874171910072507
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700874171910072504}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1a1bfe72fbc87d04e885296b53e91c66, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  target: {fileID: 0}
  smoothCameraRotation: 12
  cullingLayer:
    serializedVersion: 2
    m_Bits: 1
  lockCamera: 0
  rightOffset: 0
  defaultDistance: 2.5
  height: 1.4
  smoothFollow: 10
  xMouseSensitivity: 3
  yMouseSensitivity: 3
  yMinLimit: -40
  yMaxLimit: 80
  indexList: 0
  indexLookPoint: 0
  offSetPlayerPivot: 0
  currentStateName: 
  currentTarget: {fileID: 0}
  movementSpeed: {x: 0, y: 0}
