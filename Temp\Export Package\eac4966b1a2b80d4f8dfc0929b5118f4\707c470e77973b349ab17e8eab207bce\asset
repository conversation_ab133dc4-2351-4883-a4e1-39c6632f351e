%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: ramp60
  serializedVersion: 8
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 72
    topology: 0
    firstVertex: 0
    vertexCount: 36
    localAABB:
      m_Center: {x: 6.539916, y: 0.87553036, z: -2}
      m_Extent: {x: 3.460085, y: 0.124469995, z: 1.000001}
  - serializedVersion: 2
    firstByte: 144
    indexCount: 12
    topology: 0
    firstVertex: 36
    vertexCount: 8
    localAABB:
      m_Center: {x: 6.539916, y: 0.99999994, z: -2.000001}
      m_Extent: {x: 3.460085, y: 0.0000004172325, z: 1.0000001}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexBuffer: 000001000200010003000200040005000600050007000600080009000a000b000a0009000c000d000e000e000f000c001000110012001300120011000a000b001400150014000b00140015001600170016001500180019001a001b001a0019001c000c000f000f001d001c001e001c001d001d001f001e00200010002100120021001000220020002300210023002000240025002600270026002500280029002a002b002a002900
  m_Skin: []
  m_VertexData:
    m_CurrentChannels: 159
    m_VertexCount: 44
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 40
      format: 0
      dimension: 2
    - stream: 0
      offset: 48
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 56
      format: 0
      dimension: 4
    m_DataSize: 3168
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 6.539916, y: 0.87553036, z: -2}
    m_Extent: {x: 3.460085, y: 0.124469995, z: 1.000001}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshOptimized: 0
