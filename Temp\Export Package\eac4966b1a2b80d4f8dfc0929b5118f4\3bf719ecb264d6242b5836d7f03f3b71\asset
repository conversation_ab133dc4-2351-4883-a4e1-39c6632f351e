%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12001, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: welcomeWindowSkin
  m_EditorClassIdentifier: 
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_box:
    m_Name: box
    m_Normal:
      m_Background: {fileID: 2800000, guid: 74661375bb0a1fd42bb3297c24c9e2c5, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.9339623, g: 0.9339623, b: 0.9339623, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 6
      m_Right: 6
      m_Top: 6
      m_Bottom: 6
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_button:
    m_Name: button
    m_Normal:
      m_Background: {fileID: 2800000, guid: 402805c5625269b49beb34649a86a4d1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 2800000, guid: 402805c5625269b49beb34649a86a4d1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 402805c5625269b49beb34649a86a4d1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 402805c5625269b49beb34649a86a4d1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnHover:
      m_Background: {fileID: 2800000, guid: 402805c5625269b49beb34649a86a4d1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 402805c5625269b49beb34649a86a4d1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 6
      m_Right: 6
      m_Top: 6
      m_Bottom: 4
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 6
      m_Right: 6
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 30
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_toggle:
    m_Name: toggle
    m_Normal:
      m_Background: {fileID: 11018, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.9150943, g: 0.9107778, b: 0.9107778, a: 1}
    m_Hover:
      m_Background: {fileID: 11014, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8113208, g: 0.8113208, b: 0.8113208, a: 1}
    m_Active:
      m_Background: {fileID: 11013, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7924528, g: 0.78123885, b: 0.78123885, a: 1}
    m_OnNormal:
      m_Background: {fileID: 11016, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8773585, g: 0.8773585, b: 0.8773585, a: 1}
    m_OnHover:
      m_Background: {fileID: 11015, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnActive:
      m_Background: {fileID: 11017, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 14
      m_Right: 0
      m_Top: 14
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 15
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Overflow:
      m_Left: -1
      m_Right: 0
      m_Top: -4
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: ff3a015580ef046ac85fa087703e0a2d, type: 3}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -1}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_label:
    m_Name: label
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8962264, g: 0.8962264, b: 0.8962264, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 13
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_textField:
    m_Name: textfield
    m_Normal:
      m_Background: {fileID: 11024, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.79999995, g: 0.79999995, b: 0.79999995, a: 1}
    m_Hover:
      m_Background: {fileID: 11026, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.9, g: 0.9, b: 0.9, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 11026, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnNormal:
      m_Background: {fileID: 11025, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 3
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_textArea:
    m_Name: textarea
    m_Normal:
      m_Background: {fileID: 11024, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.9019608, g: 0.9019608, b: 0.9019608, a: 1}
    m_Hover:
      m_Background: {fileID: 11026, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.79999995, g: 0.79999995, b: 0.79999995, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 11025, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_window:
    m_Name: window
    m_Normal:
      m_Background: {fileID: 2800000, guid: 66ea7f6b43ba67c438c6ecebcc5a5c0c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 11022, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 8
      m_Right: 8
      m_Top: 8
      m_Bottom: 8
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 10
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalSlider:
    m_Name: horizontalslider
    m_Normal:
      m_Background: {fileID: 11009, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -2
      m_Bottom: -3
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 12
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalSliderThumb:
    m_Name: horizontalsliderthumb
    m_Normal:
      m_Background: {fileID: 11011, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 11012, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 11010, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 7
      m_Right: 7
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 12
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalSlider:
    m_Name: verticalslider
    m_Normal:
      m_Background: {fileID: 11021, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: -1
    m_Overflow:
      m_Left: -2
      m_Right: -3
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 12
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_verticalSliderThumb:
    m_Name: verticalsliderthumb
    m_Normal:
      m_Background: {fileID: 11011, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 11012, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 11010, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 7
      m_Bottom: 7
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: -1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 12
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_horizontalScrollbar:
    m_Name: horizontalscrollbar
    m_Normal:
      m_Background: {fileID: 11008, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 9
      m_Right: 9
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 1
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarThumb:
    m_Name: horizontalscrollbarthumb
    m_Normal:
      m_Background: {fileID: 11007, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 6
      m_Right: 6
      m_Top: 6
      m_Bottom: 6
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 6
      m_Right: 6
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 13
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarLeftButton:
    m_Name: horizontalscrollbarleftbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarRightButton:
    m_Name: horizontalscrollbarrightbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbar:
    m_Name: verticalscrollbar
    m_Normal:
      m_Background: {fileID: 2800000, guid: 852a86a871b25ff4ea50a13a4c391999, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 10
      m_Right: 10
      m_Top: 10
      m_Bottom: 10
    m_Margin:
      m_Left: 1
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbarThumb:
    m_Name: verticalscrollbarthumb
    m_Normal:
      m_Background: {fileID: 10906, guid: 0000000000000000f000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 12
      m_Bottom: 12
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_verticalScrollbarUpButton:
    m_Name: verticalscrollbarupbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbarDownButton:
    m_Name: verticalscrollbardownbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_ScrollView:
    m_Name: scrollview
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_CustomStyles:
  - m_Name: Title
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8962264, g: 0.8962264, b: 0.8962264, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: PopUp
    m_Normal:
      m_Background: {fileID: 2800000, guid: 66ea7f6b43ba67c438c6ecebcc5a5c0c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8962264, g: 0.8962264, b: 0.8962264, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 494fe6d88cfabef49b7578a4c0be1e39, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnHover:
      m_Background: {fileID: 2800000, guid: 494fe6d88cfabef49b7578a4c0be1e39, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 1
      m_Right: 1
      m_Top: 1
      m_Bottom: 1
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 5
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 12
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 14
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -5}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: CustomButton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8490566, g: 0.8490566, b: 0.8490566, a: 1}
    m_Hover:
      m_Background: {fileID: 2800000, guid: d5bee62c9974267469602e2c42dda181, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 494fe6d88cfabef49b7578a4c0be1e39, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnHover:
      m_Background: {fileID: 2800000, guid: 494fe6d88cfabef49b7578a4c0be1e39, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.6039216, g: 0.7372549, b: 0.015686275, a: 1}
    m_Border:
      m_Left: 6
      m_Right: 6
      m_Top: 6
      m_Bottom: 4
    m_Margin:
      m_Left: 5
      m_Right: 0
      m_Top: 0
      m_Bottom: 4
    m_Padding:
      m_Left: 6
      m_Right: 6
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: 6d5f66926f86d304eadada78d16964b0, type: 3}
    m_FontSize: 30
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 280}
    m_FixedWidth: 0
    m_FixedHeight: 317
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: PopUpArrow
    m_Normal:
      m_Background: {fileID: 2800000, guid: dc1ddc3a350682b47b97a43d6f349b0d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8962264, g: 0.8962264, b: 0.8962264, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.5377358, g: 0.5377358, b: 0.5377358, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 1
      m_Right: 1
      m_Top: 1
      m_Bottom: 1
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 5
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 12
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 14
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 27
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Description
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8962264, g: 0.8962264, b: 0.8962264, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 12
    m_FontStyle: 2
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: SuperTitle
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8962264, g: 0.8962264, b: 0.8962264, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 20
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2205882, g: 0.2205882, b: 0.2205882, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: PlayModeIcon
    m_Normal:
      m_Background: {fileID: 2800000, guid: 0e87adf7dc073204ca818b06d3d6fe56, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2205882, g: 0.2205882, b: 0.2205882, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 50
    m_FixedHeight: 50
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: GizmosIcon
    m_Normal:
      m_Background: {fileID: 2800000, guid: f9a1b0c879d687b43a57f6087deb9a1f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2205882, g: 0.2205882, b: 0.2205882, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 160
    m_FixedHeight: 51
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ShooterControllerIcon
    m_Normal:
      m_Background: {fileID: 2800000, guid: 6c5d1b15d970101418b18830d15bea5d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2205882, g: 0.2205882, b: 0.2205882, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 50
    m_FixedHeight: 50
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: WeaponIcon
    m_Normal:
      m_Background: {fileID: 2800000, guid: 5ca26f94969346345833c7d6ab308911, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2205882, g: 0.2205882, b: 0.2205882, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 50
    m_FixedHeight: 50
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: WindowBG
    m_Normal:
      m_Background: {fileID: 2800000, guid: 74661375bb0a1fd42bb3297c24c9e2c5, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8679245, g: 0.8679245, b: 0.8679245, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 1
      m_Right: 1
      m_Top: 1
      m_Bottom: 1
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 10
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 1
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TemplatesPromo
    m_Normal:
      m_Background: {fileID: 2800000, guid: 38ad7ac5d5d785146a42d2dc6e4f7e42, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8679245, g: 0.8679245, b: 0.8679245, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 1
      m_Right: 1
      m_Top: 1
      m_Bottom: 1
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 10
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 12800000, guid: a8f74dbf0f81e504bbbdf3f6063e1047, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 1
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 325
    m_StretchWidth: 1
    m_StretchHeight: 1
  m_Settings:
    m_DoubleClickSelectsWord: 1
    m_TripleClickSelectsLine: 1
    m_CursorColor: {r: 1, g: 1, b: 1, a: 1}
    m_CursorFlashSpeed: -1
    m_SelectionColor: {r: 1, g: 0.38403907, b: 0, a: 0.7}
