Shader "Custom/topanddownsideremove"
{
    Properties
    {
        _Color ("Color", Color) = (1,1,1,1)
        _MainTex ("Albedo (RGB)", 2D) = "white" {}
        _Glossiness ("Smoothness", Range(0,1)) = 0.5
        _Metallic ("Metallic", Range(0,1)) = 0.0
        _Transparency ("Side Transparency", Range(0,1)) = 0.5
        _EmissionColor ("Emission Color", Color) = (0,1,1,1)
        _EmissionIntensity ("Emission Intensity", Range(0,5)) = 1.0
        _BlinkSpeed ("Blink Speed", Range(0.1,10)) = 2.0
        _BlinkIntensity ("Blink Intensity", Range(0,1)) = 0.5
    }
    SubShader
    {
        Tags { "Queue"="Transparent" "RenderType"="Transparent" }
        LOD 200

        Blend SrcAlpha OneMinusSrcAlpha
        ZWrite Off

        CGPROGRAM
        // Physically based Standard lighting model, and enable shadows on all light types
        #pragma surface surf Standard fullforwardshadows

        // Use shader model 3.0 target, to get nicer looking lighting
        #pragma target 3.0

        sampler2D _MainTex;

        struct Input
        {
            float2 uv_MainTex;
            float3 worldNormal;
        };

        half _Glossiness;
        half _Metallic;
        fixed4 _Color;
        half _Transparency;
        fixed4 _EmissionColor;
        half _EmissionIntensity;
        half _BlinkSpeed;
        half _BlinkIntensity;

        // Add instancing support for this shader. You need to check 'Enable Instancing' on materials that use the shader.
        // See https://docs.unity3d.com/Manual/GPUInstancing.html for more information about instancing.
        // #pragma instancing_options assumeuniformscaling
        UNITY_INSTANCING_BUFFER_START(Props)
            // put more per-instance properties here
        UNITY_INSTANCING_BUFFER_END(Props)

        void surf (Input IN, inout SurfaceOutputStandard o)
        {
            // Discard top and bottom faces based on world normal
            float3 worldNormal = normalize(IN.worldNormal);

            // Check if the normal is pointing up (top face) or down (bottom face)
            // Using a threshold to account for slight variations in normals
            if (abs(worldNormal.y) > 0.9)
            {
                discard;
            }

            // Albedo comes from a texture tinted by color
            fixed4 c = tex2D (_MainTex, IN.uv_MainTex) * _Color;
            o.Albedo = c.rgb;
            // Metallic and smoothness come from slider variables
            o.Metallic = _Metallic;
            o.Smoothness = _Glossiness;
            // Apply transparency to remaining sides
            o.Alpha = c.a * _Transparency;
        }
        ENDCG
    }
    FallBack "Diffuse"
}
