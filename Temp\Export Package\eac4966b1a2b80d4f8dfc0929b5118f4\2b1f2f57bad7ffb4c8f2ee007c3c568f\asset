%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: platform
  serializedVersion: 8
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: 0.0000030398369, y: 0.2734146, z: 0.000011444092}
      m_Extent: {x: 0.5, y: 0.023414612, z: 0.5}
  - serializedVersion: 2
    firstByte: 72
    indexCount: 30
    topology: 0
    firstVertex: 24
    vertexCount: 20
    localAABB:
      m_Center: {x: 0.0000015199184, y: 0.125, z: 0.000005722046}
      m_Extent: {x: 0.50000155, y: 0.125, z: 0.5000057}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexBuffer: 000001000200010003000200040005000600050007000600080009000a0009000b000a000c000d000e000d000f000e00100011001200110013001200140015001600150017001600180019001a0019001b001a001c001d001e001d001f001e00200021002200210023002200240025002600250027002600280029002a0029002b002a00
  m_Skin: []
  m_VertexData:
    m_CurrentChannels: 159
    m_VertexCount: 44
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 40
      format: 0
      dimension: 2
    - stream: 0
      offset: 48
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 56
      format: 0
      dimension: 4
    m_DataSize: 3168
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.0000015199184, y: 0.14841461, z: 0.000005722046}
    m_Extent: {x: 0.50000155, y: 0.14841461, z: 0.5000057}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshOptimized: 0
