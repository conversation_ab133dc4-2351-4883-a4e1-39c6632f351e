%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: ramp45
  serializedVersion: 8
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 72
    topology: 0
    firstVertex: 0
    vertexCount: 36
    localAABB:
      m_Center: {x: 5.754188, y: 0.8530837, z: -2.0000007}
      m_Extent: {x: 4.245813, y: 0.14691743, z: 1.0000012}
  - serializedVersion: 2
    firstByte: 144
    indexCount: 12
    topology: 0
    firstVertex: 36
    vertexCount: 8
    localAABB:
      m_Center: {x: 5.754188, y: 1.0000004, z: -2.0000007}
      m_Extent: {x: 4.245813, y: 0.00000086426735, z: 1.0000002}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexBuffer: 000001000200010003000200040005000600050007000600080009000a000a000b0008000c000d000e000e000f000c00100011001200120013001000140008000b000b0015001400160014001700150017001400180019001a001b001a0019001c000c000f000f001d001c001e001c001d001d001f001e00200021001100110010002000220023002100210020002200240025002600260027002400280029002a002b002a002900
  m_Skin: []
  m_VertexData:
    m_CurrentChannels: 159
    m_VertexCount: 44
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 40
      format: 0
      dimension: 2
    - stream: 0
      offset: 48
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 56
      format: 0
      dimension: 4
    m_DataSize: 3168
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 5.754188, y: 0.8530837, z: -2.0000007}
    m_Extent: {x: 4.245813, y: 0.14691743, z: 1.0000012}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshOptimized: 0
