%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: climbUp
  serializedVersion: 8
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 60
    topology: 0
    firstVertex: 0
    vertexCount: 40
    localAABB:
      m_Center: {x: 0.000000834465, y: -0.99999994, z: -0.00000011920929}
      m_Extent: {x: 1, y: 0.99999994, z: 1}
  - serializedVersion: 2
    firstByte: 120
    indexCount: 24
    topology: 0
    firstVertex: 40
    vertexCount: 16
    localAABB:
      m_Center: {x: 0.000000834465, y: -0.12494624, z: -0.00000011920929}
      m_Extent: {x: 1, y: 0.12494624, z: 1}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexBuffer: 000001000200010003000200040005000600050007000600080009000a000b000a0009000c000d000e000f000e000d00100011001200130012001100140015001600170016001500180019001a0019001b001a001c001d001e001d001f001e00200021002200210023002200240025002600250027002600280029002a002b002a0029002c002d002e002f002e002d00300031003200330032003100340035003600370036003500
  m_Skin: []
  m_VertexData:
    m_CurrentChannels: 159
    m_VertexCount: 56
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 40
      format: 0
      dimension: 2
    - stream: 0
      offset: 48
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 56
      format: 0
      dimension: 4
    m_DataSize: 4032
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.000000834465, y: -0.99999994, z: -0.00000011920929}
    m_Extent: {x: 1, y: 0.99999994, z: 1}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshOptimized: 0
