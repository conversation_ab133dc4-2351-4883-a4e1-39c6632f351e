fileFormatVersion: 2
guid: 340f99b8334a14c4a8a01dce6e1e5348
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: Armature
    100002: VBOT_:Head
    100004: VBOT_:HeadTop_End
    100006: VBOT_:HeadTop_End_end
    100008: VBOT_:Hips
    100010: VBOT_:LeftArm
    100012: VBOT_:LeftFoot
    100014: VBOT_:LeftForeArm
    100016: VBOT_:LeftHand
    100018: VBOT_:LeftHandIndex1
    100020: VBOT_:LeftHandIndex2
    100022: VBOT_:LeftHandIndex3
    100024: VBOT_:LeftHandIndex4
    100026: VBOT_:LeftHandIndex4_end
    100028: VBOT_:LeftHandMiddle1
    100030: VBOT_:LeftHandMiddle2
    100032: VBOT_:LeftHandMiddle3
    100034: VBOT_:LeftHandMiddle4
    100036: VBOT_:LeftHandMiddle4_end
    100038: VBOT_:LeftHandPinky1
    100040: VBOT_:LeftHandPinky2
    100042: VBOT_:LeftHandPinky3
    100044: VBOT_:LeftHandPinky4
    100046: VBOT_:LeftHandPinky4_end
    100048: VBOT_:LeftHandRing1
    100050: VBOT_:LeftHandRing2
    100052: VBOT_:LeftHandRing3
    100054: VBOT_:LeftHandRing4
    100056: VBOT_:LeftHandRing4_end
    100058: VBOT_:LeftHandThumb1
    100060: VBOT_:LeftHandThumb2
    100062: VBOT_:LeftHandThumb3
    100064: VBOT_:LeftHandThumb4
    100066: VBOT_:LeftHandThumb4_end
    100068: VBOT_:LeftLeg
    100070: VBOT_:LeftShoulder
    100072: VBOT_:LeftToe_End
    100074: VBOT_:LeftToe_End_end
    100076: VBOT_:LeftToeBase
    100078: VBOT_:LeftUpLeg
    100080: VBOT_:Neck
    100082: VBOT_:RightArm
    100084: VBOT_:RightFoot
    100086: VBOT_:RightForeArm
    100088: VBOT_:RightHand
    100090: VBOT_:RightHandIndex1
    100092: VBOT_:RightHandIndex2
    100094: VBOT_:RightHandIndex3
    100096: VBOT_:RightHandIndex4
    100098: VBOT_:RightHandIndex4_end
    100100: VBOT_:RightHandMiddle1
    100102: VBOT_:RightHandMiddle2
    100104: VBOT_:RightHandMiddle3
    100106: VBOT_:RightHandMiddle4
    100108: VBOT_:RightHandMiddle4_end
    100110: VBOT_:RightHandPinky1
    100112: VBOT_:RightHandPinky2
    100114: VBOT_:RightHandPinky3
    100116: VBOT_:RightHandPinky4
    100118: VBOT_:RightHandPinky4_end
    100120: VBOT_:RightHandRing1
    100122: VBOT_:RightHandRing2
    100124: VBOT_:RightHandRing3
    100126: VBOT_:RightHandRing4
    100128: VBOT_:RightHandRing4_end
    100130: VBOT_:RightHandThumb1
    100132: VBOT_:RightHandThumb2
    100134: VBOT_:RightHandThumb3
    100136: VBOT_:RightHandThumb4
    100138: VBOT_:RightHandThumb4_end
    100140: VBOT_:RightLeg
    100142: VBOT_:RightShoulder
    100144: VBOT_:RightToe_End
    100146: VBOT_:RightToe_End_end
    100148: VBOT_:RightToeBase
    100150: VBOT_:RightUpLeg
    100152: VBOT_:Spine
    100154: VBOT_:Spine1
    100156: VBOT_:Spine2
    100158: VBOT_:VBOT_LOD0
    100160: VBOT_:VBOT_LOD1
    100162: VBOT_:VBOT_LOD2
    100164: VBOT_:VBOT_LOD3
    100166: //RootNode
    400000: Armature
    400002: VBOT_:Head
    400004: VBOT_:HeadTop_End
    400006: VBOT_:HeadTop_End_end
    400008: VBOT_:Hips
    400010: VBOT_:LeftArm
    400012: VBOT_:LeftFoot
    400014: VBOT_:LeftForeArm
    400016: VBOT_:LeftHand
    400018: VBOT_:LeftHandIndex1
    400020: VBOT_:LeftHandIndex2
    400022: VBOT_:LeftHandIndex3
    400024: VBOT_:LeftHandIndex4
    400026: VBOT_:LeftHandIndex4_end
    400028: VBOT_:LeftHandMiddle1
    400030: VBOT_:LeftHandMiddle2
    400032: VBOT_:LeftHandMiddle3
    400034: VBOT_:LeftHandMiddle4
    400036: VBOT_:LeftHandMiddle4_end
    400038: VBOT_:LeftHandPinky1
    400040: VBOT_:LeftHandPinky2
    400042: VBOT_:LeftHandPinky3
    400044: VBOT_:LeftHandPinky4
    400046: VBOT_:LeftHandPinky4_end
    400048: VBOT_:LeftHandRing1
    400050: VBOT_:LeftHandRing2
    400052: VBOT_:LeftHandRing3
    400054: VBOT_:LeftHandRing4
    400056: VBOT_:LeftHandRing4_end
    400058: VBOT_:LeftHandThumb1
    400060: VBOT_:LeftHandThumb2
    400062: VBOT_:LeftHandThumb3
    400064: VBOT_:LeftHandThumb4
    400066: VBOT_:LeftHandThumb4_end
    400068: VBOT_:LeftLeg
    400070: VBOT_:LeftShoulder
    400072: VBOT_:LeftToe_End
    400074: VBOT_:LeftToe_End_end
    400076: VBOT_:LeftToeBase
    400078: VBOT_:LeftUpLeg
    400080: VBOT_:Neck
    400082: VBOT_:RightArm
    400084: VBOT_:RightFoot
    400086: VBOT_:RightForeArm
    400088: VBOT_:RightHand
    400090: VBOT_:RightHandIndex1
    400092: VBOT_:RightHandIndex2
    400094: VBOT_:RightHandIndex3
    400096: VBOT_:RightHandIndex4
    400098: VBOT_:RightHandIndex4_end
    400100: VBOT_:RightHandMiddle1
    400102: VBOT_:RightHandMiddle2
    400104: VBOT_:RightHandMiddle3
    400106: VBOT_:RightHandMiddle4
    400108: VBOT_:RightHandMiddle4_end
    400110: VBOT_:RightHandPinky1
    400112: VBOT_:RightHandPinky2
    400114: VBOT_:RightHandPinky3
    400116: VBOT_:RightHandPinky4
    400118: VBOT_:RightHandPinky4_end
    400120: VBOT_:RightHandRing1
    400122: VBOT_:RightHandRing2
    400124: VBOT_:RightHandRing3
    400126: VBOT_:RightHandRing4
    400128: VBOT_:RightHandRing4_end
    400130: VBOT_:RightHandThumb1
    400132: VBOT_:RightHandThumb2
    400134: VBOT_:RightHandThumb3
    400136: VBOT_:RightHandThumb4
    400138: VBOT_:RightHandThumb4_end
    400140: VBOT_:RightLeg
    400142: VBOT_:RightShoulder
    400144: VBOT_:RightToe_End
    400146: VBOT_:RightToe_End_end
    400148: VBOT_:RightToeBase
    400150: VBOT_:RightUpLeg
    400152: VBOT_:Spine
    400154: VBOT_:Spine1
    400156: VBOT_:Spine2
    400158: VBOT_:VBOT_LOD0
    400160: VBOT_:VBOT_LOD1
    400162: VBOT_:VBOT_LOD2
    400164: VBOT_:VBOT_LOD3
    400166: //RootNode
    4300000: VBOT_:VBOT_LOD3
    4300002: VBOT_:VBOT_LOD2
    4300004: VBOT_:VBOT_LOD1
    4300006: VBOT_:VBOT_LOD0
    7400000: T-pose
    9500000: //RootNode
    13700000: VBOT_:VBOT_LOD0
    13700002: VBOT_:VBOT_LOD1
    13700004: VBOT_:VBOT_LOD2
    13700006: VBOT_:VBOT_LOD3
    20500000: //RootNode
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations:
    - serializedVersion: 16
      name: T-pose
      takeName: Armature|Armature|Take 001|BaseLayer
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Armature
        weight: 1
      - path: Armature/VBOT_:Hips
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:LeftUpLeg
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:LeftUpLeg/VBOT_:LeftLeg
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:LeftUpLeg/VBOT_:LeftLeg/VBOT_:LeftFoot
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:LeftUpLeg/VBOT_:LeftLeg/VBOT_:LeftFoot/VBOT_:LeftToeBase
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:LeftUpLeg/VBOT_:LeftLeg/VBOT_:LeftFoot/VBOT_:LeftToeBase/VBOT_:LeftToe_End
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:LeftUpLeg/VBOT_:LeftLeg/VBOT_:LeftFoot/VBOT_:LeftToeBase/VBOT_:LeftToe_End/VBOT_:LeftToe_End_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:RightUpLeg
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:RightUpLeg/VBOT_:RightLeg
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:RightUpLeg/VBOT_:RightLeg/VBOT_:RightFoot
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:RightUpLeg/VBOT_:RightLeg/VBOT_:RightFoot/VBOT_:RightToeBase
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:RightUpLeg/VBOT_:RightLeg/VBOT_:RightFoot/VBOT_:RightToeBase/VBOT_:RightToe_End
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:RightUpLeg/VBOT_:RightLeg/VBOT_:RightFoot/VBOT_:RightToeBase/VBOT_:RightToe_End/VBOT_:RightToe_End_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandIndex1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandIndex1/VBOT_:LeftHandIndex2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandIndex1/VBOT_:LeftHandIndex2/VBOT_:LeftHandIndex3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandIndex1/VBOT_:LeftHandIndex2/VBOT_:LeftHandIndex3/VBOT_:LeftHandIndex4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandIndex1/VBOT_:LeftHandIndex2/VBOT_:LeftHandIndex3/VBOT_:LeftHandIndex4/VBOT_:LeftHandIndex4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandMiddle1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandMiddle1/VBOT_:LeftHandMiddle2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandMiddle1/VBOT_:LeftHandMiddle2/VBOT_:LeftHandMiddle3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandMiddle1/VBOT_:LeftHandMiddle2/VBOT_:LeftHandMiddle3/VBOT_:LeftHandMiddle4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandMiddle1/VBOT_:LeftHandMiddle2/VBOT_:LeftHandMiddle3/VBOT_:LeftHandMiddle4/VBOT_:LeftHandMiddle4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandPinky1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandPinky1/VBOT_:LeftHandPinky2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandPinky1/VBOT_:LeftHandPinky2/VBOT_:LeftHandPinky3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandPinky1/VBOT_:LeftHandPinky2/VBOT_:LeftHandPinky3/VBOT_:LeftHandPinky4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandPinky1/VBOT_:LeftHandPinky2/VBOT_:LeftHandPinky3/VBOT_:LeftHandPinky4/VBOT_:LeftHandPinky4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandRing1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandRing1/VBOT_:LeftHandRing2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandRing1/VBOT_:LeftHandRing2/VBOT_:LeftHandRing3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandRing1/VBOT_:LeftHandRing2/VBOT_:LeftHandRing3/VBOT_:LeftHandRing4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandRing1/VBOT_:LeftHandRing2/VBOT_:LeftHandRing3/VBOT_:LeftHandRing4/VBOT_:LeftHandRing4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandThumb1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandThumb1/VBOT_:LeftHandThumb2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandThumb1/VBOT_:LeftHandThumb2/VBOT_:LeftHandThumb3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandThumb1/VBOT_:LeftHandThumb2/VBOT_:LeftHandThumb3/VBOT_:LeftHandThumb4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:LeftShoulder/VBOT_:LeftArm/VBOT_:LeftForeArm/VBOT_:LeftHand/VBOT_:LeftHandThumb1/VBOT_:LeftHandThumb2/VBOT_:LeftHandThumb3/VBOT_:LeftHandThumb4/VBOT_:LeftHandThumb4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:Neck
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:Neck/VBOT_:Head
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:Neck/VBOT_:Head/VBOT_:HeadTop_End
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:Neck/VBOT_:Head/VBOT_:HeadTop_End/VBOT_:HeadTop_End_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandIndex1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandIndex1/VBOT_:RightHandIndex2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandIndex1/VBOT_:RightHandIndex2/VBOT_:RightHandIndex3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandIndex1/VBOT_:RightHandIndex2/VBOT_:RightHandIndex3/VBOT_:RightHandIndex4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandIndex1/VBOT_:RightHandIndex2/VBOT_:RightHandIndex3/VBOT_:RightHandIndex4/VBOT_:RightHandIndex4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandMiddle1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandMiddle1/VBOT_:RightHandMiddle2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandMiddle1/VBOT_:RightHandMiddle2/VBOT_:RightHandMiddle3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandMiddle1/VBOT_:RightHandMiddle2/VBOT_:RightHandMiddle3/VBOT_:RightHandMiddle4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandMiddle1/VBOT_:RightHandMiddle2/VBOT_:RightHandMiddle3/VBOT_:RightHandMiddle4/VBOT_:RightHandMiddle4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandPinky1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandPinky1/VBOT_:RightHandPinky2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandPinky1/VBOT_:RightHandPinky2/VBOT_:RightHandPinky3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandPinky1/VBOT_:RightHandPinky2/VBOT_:RightHandPinky3/VBOT_:RightHandPinky4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandPinky1/VBOT_:RightHandPinky2/VBOT_:RightHandPinky3/VBOT_:RightHandPinky4/VBOT_:RightHandPinky4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandRing1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandRing1/VBOT_:RightHandRing2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandRing1/VBOT_:RightHandRing2/VBOT_:RightHandRing3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandRing1/VBOT_:RightHandRing2/VBOT_:RightHandRing3/VBOT_:RightHandRing4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandRing1/VBOT_:RightHandRing2/VBOT_:RightHandRing3/VBOT_:RightHandRing4/VBOT_:RightHandRing4_end
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandThumb1
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandThumb1/VBOT_:RightHandThumb2
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandThumb1/VBOT_:RightHandThumb2/VBOT_:RightHandThumb3
        weight: 1
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandThumb1/VBOT_:RightHandThumb2/VBOT_:RightHandThumb3/VBOT_:RightHandThumb4
        weight: 0
      - path: Armature/VBOT_:Hips/VBOT_:Spine/VBOT_:Spine1/VBOT_:Spine2/VBOT_:RightShoulder/VBOT_:RightArm/VBOT_:RightForeArm/VBOT_:RightHand/VBOT_:RightHandThumb1/VBOT_:RightHandThumb2/VBOT_:RightHandThumb3/VBOT_:RightHandThumb4/VBOT_:RightHandThumb4_end
        weight: 0
      - path: VBOT_:VBOT_LOD0
        weight: 0
      - path: VBOT_:VBOT_LOD1
        weight: 0
      - path: VBOT_:VBOT_LOD2
        weight: 0
      - path: VBOT_:VBOT_LOD3
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.0625
    - 0.01
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human:
    - boneName: VBOT_:Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: VBOT_:Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: VBOT_LOD(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0.000000081460335, y: -0, z: 0, w: 1}
      scale: {x: 0.01, y: 0.01, z: 0.01}
    - name: VBOT_:Hips
      parentName: 
      position: {x: 0.00019899552, y: 104.913734, z: -1.9585699}
      rotation: {x: -0.00000017530357, y: 0.00000047677122, z: -0.000014443002, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:Spine
      parentName: 
      position: {x: -1.6871125e-10, y: 9.702705, z: -0.15236208}
      rotation: {x: -0.007850627, y: -0.0000005901344, z: 0.000014438815, w: 0.9999692}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: VBOT_:Spine1
      parentName: 
      position: {x: 1.02090515e-11, y: 11.321213, z: -0.0000001788139}
      rotation: {x: 0.0000000046566133, y: -5.333572e-14, z: 6.8136756e-13, w: 1}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: VBOT_:Spine2
      parentName: 
      position: {x: -4.7400968e-12, y: 12.938553, z: 0.00000017881393}
      rotation: {x: -0.000000011175873, y: 1.3353475e-18, z: -8.1315156e-20, w: 1}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: VBOT_:Neck
      parentName: 
      position: {x: -1.8170866e-11, y: 14.5558195, z: -0.0000001788139}
      rotation: {x: 0.00785081, y: 4.084771e-14, z: 2.2770148e-13, w: 0.9999692}
      scale: {x: 1, y: 0.9999996, z: 0.99999964}
    - name: VBOT_:Head
      parentName: 
      position: {x: -7.3683655e-12, y: 5.6972804, z: 2.4140098}
      rotation: {x: 1.6653348e-16, y: 4.387005e-19, z: 9.1814114e-20, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:HeadTop_End
      parentName: 
      position: {x: -1.5957928e-11, y: 20.432783, z: 8.657652}
      rotation: {x: 1.6653348e-16, y: 4.387005e-19, z: 9.1814114e-20, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:HeadTop_End_end
      parentName: 
      position: {x: -0, y: 22.191282, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftShoulder
      parentName: 
      position: {x: -6.233017, y: 12.723451, z: -0.060752567}
      rotation: {x: 0.56555074, y: -0.42279127, z: 0.5711599, w: 0.41854063}
      scale: {x: 0.99999946, y: 0.9999997, z: 0.9999995}
    - name: VBOT_:LeftArm
      parentName: 
      position: {x: -0.00000041723249, y: 12.885075, z: 0.000009480863}
      rotation: {x: -0.1476417, y: 0.007941283, z: -0.00621769, w: 0.9889895}
      scale: {x: 0.9999999, y: 1.0000001, z: 1.0000001}
    - name: VBOT_:LeftForeArm
      parentName: 
      position: {x: -0.00000024976904, y: 28.782936, z: 0.000004796884}
      rotation: {x: 0.0000014798451, y: 0.00010023918, z: 0.00000030412875, w: 1}
      scale: {x: 1.0000001, y: 0.99999964, z: 0.9999999}
    - name: VBOT_:LeftHand
      parentName: 
      position: {x: -0.00000031946908, y: 29.885254, z: -0.0000056620333}
      rotation: {x: -0.000122516, y: -0.012010347, z: 0.057643548, w: 0.99826497}
      scale: {x: 1.0000005, y: 1, z: 1.0000001}
    - name: VBOT_:LeftHandThumb1
      parentName: 
      position: {x: 2.207032, y: 2.8915968, z: 1.3849902}
      rotation: {x: 0.06278457, y: -0.026700797, z: -0.4749348, w: 0.87737226}
      scale: {x: 1, y: 1.0000002, z: 0.99999964}
    - name: VBOT_:LeftHandThumb2
      parentName: 
      position: {x: -0.4968084, y: 3.0740163, z: 0.000013850628}
      rotation: {x: 0.00008349873, y: -0.003154348, z: 0.13292927, w: 0.9911205}
      scale: {x: 1, y: 1, z: 1.0000001}
    - name: VBOT_:LeftHandThumb3
      parentName: 
      position: {x: 0.37320492, y: 3.4936538, z: 0.00000017881392}
      rotation: {x: -0.00010924415, y: -0.0015527824, z: -0.035468064, w: 0.9993696}
      scale: {x: 1, y: 0.9999999, z: 0.99999946}
    - name: VBOT_:LeftHandThumb4
      parentName: 
      position: {x: 0.12360834, y: 3.4833553, z: -0.0000096559515}
      rotation: {x: 0.000000011175872, y: 0.000000011175872, z: 0.000000056810677,
        w: 1}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: VBOT_:LeftHandThumb4_end
      parentName: 
      position: {x: -0, y: 3.4855406, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftHandIndex1
      parentName: 
      position: {x: 3.306355, y: 10.214577, z: 0.16185996}
      rotation: {x: -0.00057633274, y: -0.00008826893, z: -0.057947285, w: 0.9983195}
      scale: {x: 0.9999994, y: 1.0000005, z: 0.99999946}
    - name: VBOT_:LeftHandIndex2
      parentName: 
      position: {x: -0.0022915674, y: 3.742948, z: -0.0000026019695}
      rotation: {x: 0.00000022857085, y: 0.000003505723, z: 0.0004560625, w: 0.99999994}
      scale: {x: 0.99999994, y: 0.99999964, z: 1.0000001}
    - name: VBOT_:LeftHandIndex3
      parentName: 
      position: {x: 0.0010102539, y: 3.357671, z: 0.0000006369955}
      rotation: {x: 0.00000029828897, y: -0.0000003028567, z: 0.00006260211, w: 1}
      scale: {x: 1, y: 0.99999976, z: 1}
    - name: VBOT_:LeftHandIndex4
      parentName: 
      position: {x: 0.0012805796, y: 3.0745363, z: -0.000006300746}
      rotation: {x: -0.00000008045498, y: 2.508182e-10, z: -5.8730437e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftHandIndex4_end
      parentName: 
      position: {x: -0, y: 3.07454, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftHandMiddle1
      parentName: 
      position: {x: 1.1613122, y: 10.023253, z: -0.011463714}
      rotation: {x: -0.0007780783, y: -0.0035744617, z: -0.057712052, w: 0.9983266}
      scale: {x: 1, y: 1.0000005, z: 0.9999997}
    - name: VBOT_:LeftHandMiddle2
      parentName: 
      position: {x: -0.00060562877, y: 4.0604153, z: 0.000001076325}
      rotation: {x: 0.00000047429356, y: -0.0000044267067, z: -0.00016212398, w: 1}
      scale: {x: 1, y: 0.9999993, z: 1.0000004}
    - name: VBOT_:LeftHandMiddle3
      parentName: 
      position: {x: -0.0018412619, y: 3.8137584, z: 0.0000071693207}
      rotation: {x: 0.0000016969184, y: -0.000004723498, z: 0.0005666729, w: 0.9999999}
      scale: {x: 0.99999994, y: 1.0000006, z: 0.99999976}
    - name: VBOT_:LeftHandMiddle4
      parentName: 
      position: {x: 0.0024477316, y: 3.6274889, z: -0.0000036316922}
      rotation: {x: 0.00000005096203, y: -1.3542201e-10, z: -0.0000000016708129, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: VBOT_:LeftHandMiddle4_end
      parentName: 
      position: {x: -0, y: 3.6274853, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftHandRing1
      parentName: 
      position: {x: -1.3299953, y: 10.050946, z: -0.0104290685}
      rotation: {x: -0.0007830648, y: -0.003655989, z: -0.05754699, w: 0.99833584}
      scale: {x: 0.9999994, y: 1.0000001, z: 0.99999976}
    - name: VBOT_:LeftHandRing2
      parentName: 
      position: {x: 0.00056505756, y: 3.2887826, z: -0.00001606815}
      rotation: {x: -0.0000003780744, y: -0.0000008826013, z: -0.00012194871, w: 1}
      scale: {x: 1, y: 0.9999999, z: 1.0000002}
    - name: VBOT_:LeftHandRing3
      parentName: 
      position: {x: -0.00015043419, y: 3.256652, z: 0.000012098185}
      rotation: {x: 0.0000019568083, y: -0.000005436704, z: -0.00003674567, w: 1}
      scale: {x: 1.0000001, y: 0.99999994, z: 0.9999996}
    - name: VBOT_:LeftHandRing4
      parentName: 
      position: {x: -0.00041358636, y: 2.9665046, z: 0.000024707986}
      rotation: {x: 0.00000003154195, y: 0.0000000055908194, z: 0.0000000044438986,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftHandRing4_end
      parentName: 
      position: {x: -0, y: 2.966476, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftHandPinky1
      parentName: 
      position: {x: -3.1376708, y: 9.433374, z: 0.09271452}
      rotation: {x: -0.0004632812, y: 0.0018745903, z: -0.05825882, w: 0.99829966}
      scale: {x: 0.99999994, y: 0.9999999, z: 0.99999994}
    - name: VBOT_:LeftHandPinky2
      parentName: 
      position: {x: -0.0039647813, y: 3.2343886, z: -0.000012723379}
      rotation: {x: -0.0000011046868, y: 0.000015655518, z: 0.0005995122, w: 0.9999998}
      scale: {x: 1.0000001, y: 0.9999998, z: 0.9999997}
    - name: VBOT_:LeftHandPinky3
      parentName: 
      position: {x: -0.00007703411, y: 2.6718638, z: 0.000018784818}
      rotation: {x: -0.00000036671616, y: 0.00006196149, z: 0.00089296716, w: 0.99999964}
      scale: {x: 0.9999999, y: 1.0000007, z: 1.0000002}
    - name: VBOT_:LeftHandPinky4
      parentName: 
      position: {x: 0.004042044, y: 2.2874355, z: 0.0000013776297}
      rotation: {x: 0.0000000027521878, y: -9.31788e-10, z: -0.000000019734088, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftHandPinky4_end
      parentName: 
      position: {x: -0, y: 2.2874296, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:RightShoulder
      parentName: 
      position: {x: 6.2330184, y: 12.722447, z: 0.0031886697}
      rotation: {x: 0.56846106, y: 0.42061523, z: -0.5682299, w: 0.42077276}
      scale: {x: 0.9999998, y: 1.0000004, z: 0.9999994}
    - name: VBOT_:RightArm
      parentName: 
      position: {x: -0.0000004218891, y: 12.885061, z: -0.0000033304093}
      rotation: {x: -0.14777023, y: -0.0063015977, z: 0.00074138155, w: 0.9890014}
      scale: {x: 0.99999994, y: 0.99999934, z: 0.9999998}
    - name: VBOT_:RightForeArm
      parentName: 
      position: {x: 0.0000005218607, y: 28.777094, z: -0.00000024959442}
      rotation: {x: 0.00000016583917, y: -0.0000813696, z: -0.0000003511705, w: 1}
      scale: {x: 0.9999995, y: 1.0000006, z: 1.0000001}
    - name: VBOT_:RightHand
      parentName: 
      position: {x: 0.00000020841252, y: 29.88406, z: 0.000014346092}
      rotation: {x: 0.0051883897, y: 0.021200966, z: -0.052770805, w: 0.9983681}
      scale: {x: 1.0000007, y: 1, z: 1}
    - name: VBOT_:RightHandThumb1
      parentName: 
      position: {x: -2.1773484, y: 2.860778, z: 1.5000123}
      rotation: {x: 0.050683524, y: 0.025704542, z: 0.46451232, w: 0.8837414}
      scale: {x: 1.0000001, y: 0.9999995, z: 0.9999996}
    - name: VBOT_:RightHandThumb2
      parentName: 
      position: {x: 0.4479191, y: 3.0840445, z: 0.0000027716158}
      rotation: {x: 0.00010331243, y: 0.003032002, z: -0.10960612, w: 0.9939705}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:RightHandThumb3
      parentName: 
      position: {x: -0.26757833, y: 3.5406926, z: 0.000021934507}
      rotation: {x: 0.000032287433, y: -0.00049903296, z: 0.011669388, w: 0.9999318}
      scale: {x: 1, y: 0.9999996, z: 1.0000001}
    - name: VBOT_:RightHandThumb4
      parentName: 
      position: {x: -0.18034028, y: 3.4578032, z: -0.00000989437}
      rotation: {x: -0.000000001862645, y: -0.0000000037252899, z: 0.000000028870998,
        w: 1}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: VBOT_:RightHandThumb4_end
      parentName: 
      position: {x: -0, y: 3.4625075, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:RightHandIndex1
      parentName: 
      position: {x: -3.2609918, y: 10.302796, z: 0.20424767}
      rotation: {x: -0.006308148, y: 0.000009262221, z: 0.052337017, w: 0.9986096}
      scale: {x: 1, y: 0.9999997, z: 0.99999976}
    - name: VBOT_:RightHandIndex2
      parentName: 
      position: {x: -0.002191752, y: 3.5275145, z: -0.0000073621045}
      rotation: {x: -0.00000036630905, y: 0.00003379532, z: -0.00061720755, w: 0.9999998}
      scale: {x: 0.99999994, y: 0.99999964, z: 1.0000005}
    - name: VBOT_:RightHandIndex3
      parentName: 
      position: {x: -0.006155073, y: 3.336971, z: 0.0000052740793}
      rotation: {x: -0.00000062817367, y: 0.000036295332, z: 0.002351098, w: 0.99999726}
      scale: {x: 0.99999994, y: 1.0000004, z: 1}
    - name: VBOT_:RightHandIndex4
      parentName: 
      position: {x: 0.008346348, y: 2.918003, z: -0.0000014519318}
      rotation: {x: 0.00000000842556, y: 0.000000092695934, z: 0.00000008829295, w: 1}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: VBOT_:RightHandIndex4_end
      parentName: 
      position: {x: -0, y: 2.9180162, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:RightHandMiddle1
      parentName: 
      position: {x: -1.0459455, y: 9.890944, z: -0.12528582}
      rotation: {x: -0.006425467, y: 0.002276494, z: 0.053364556, w: 0.99855185}
      scale: {x: 0.99999934, y: 0.9999995, z: 0.9999997}
    - name: VBOT_:RightHandMiddle2
      parentName: 
      position: {x: 0.006039291, y: 4.100815, z: 0.000013880431}
      rotation: {x: -0.0000010921335, y: -0.000014545862, z: -0.00018764834, w: 1}
      scale: {x: 1, y: 0.9999995, z: 0.9999998}
    - name: VBOT_:RightHandMiddle3
      parentName: 
      position: {x: 0.0041791643, y: 3.861288, z: -0.000013139098}
      rotation: {x: -0.0000004923334, y: 0.000086348875, z: -0.001940938, w: 0.99999815}
      scale: {x: 1, y: 1.0000007, z: 0.99999994}
    - name: VBOT_:RightHandMiddle4
      parentName: 
      position: {x: -0.010218023, y: 3.6506317, z: -0.000012870877}
      rotation: {x: -0.00000003355308, y: -7.557048e-10, z: -0.000000006678988, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:RightHandMiddle4_end
      parentName: 
      position: {x: -0, y: 3.6506531, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:RightHandRing1
      parentName: 
      position: {x: 1.0770532, y: 9.615877, z: -0.04499008}
      rotation: {x: -0.0064634937, y: 0.0029677008, z: 0.052755825, w: 0.9985821}
      scale: {x: 0.9999992, y: 0.99999964, z: 1}
    - name: VBOT_:RightHandRing2
      parentName: 
      position: {x: 0.000864461, y: 3.5615304, z: -0.000015171244}
      rotation: {x: -0.0000007118655, y: -0.00000040345734, z: -0.00024250762, w: 1}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: VBOT_:RightHandRing3
      parentName: 
      position: {x: -0.00084478216, y: 3.4413192, z: 0.000004537403}
      rotation: {x: 0.0000003853574, y: -0.0000024102292, z: 0.000116190175, w: 1}
      scale: {x: 0.99999994, y: 1, z: 0.9999997}
    - name: VBOT_:RightHandRing4
      parentName: 
      position: {x: -0.000018931923, y: 3.1309967, z: -0.000009484588}
      rotation: {x: -0.000000040627246, y: -0.000000007421774, z: 0.0000000014611032,
        w: 1}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: VBOT_:RightHandRing4_end
      parentName: 
      position: {x: -0, y: 3.1309974, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:RightHandPinky1
      parentName: 
      position: {x: 3.229885, y: 8.635466, z: 0.26456282}
      rotation: {x: -0.0067263856, y: 0.007935547, z: 0.052596204, w: 0.9985617}
      scale: {x: 0.999999, y: 0.99999946, z: 1.0000002}
    - name: VBOT_:RightHandPinky2
      parentName: 
      position: {x: 0.000006397138, y: 3.5136409, z: 0.0000012964009}
      rotation: {x: -0.00000052158674, y: 0.0000013498303, z: 0.000034841643, w: 1}
      scale: {x: 1, y: 0.9999999, z: 0.999999}
    - name: VBOT_:RightHandPinky3
      parentName: 
      position: {x: 0.00017850239, y: 2.8926263, z: -0.0000014435499}
      rotation: {x: 0.0000013946006, y: 0.00000016396328, z: -0.000073763025, w: 1}
      scale: {x: 1.0000001, y: 1.0000004, z: 1.0000002}
    - name: VBOT_:RightHandPinky4
      parentName: 
      position: {x: -0.00018684844, y: 2.5621476, z: 0.0000047087665}
      rotation: {x: 0.00000000583043, y: -0.0000000037147578, z: 0.000000024821702,
        w: 1}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: VBOT_:RightHandPinky4_end
      parentName: 
      position: {x: -0, y: 2.562149, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:LeftUpLeg
      parentName: 
      position: {x: -10.920647, y: -5.3976464, z: 1.9481007}
      rotation: {x: -0.000032394124, y: 0.0055751884, z: 0.99996626, w: 0.0060344283}
      scale: {x: 1.0000803, y: 1.0000002, z: 1.0000001}
    - name: VBOT_:LeftLeg
      parentName: 
      position: {x: 0.0000013736861, y: 46.4423, z: -0.00000012147937}
      rotation: {x: -0.045484938, y: -0.0005017409, z: 0.01265651, w: 0.99888474}
      scale: {x: 0.9999995, y: 0.9999993, z: 0.99999976}
    - name: VBOT_:LeftFoot
      parentName: 
      position: {x: 0.00000043968427, y: 42.52575, z: 0.0000009657814}
      rotation: {x: 0.48034218, y: -0.0065366975, z: -0.0049221627, w: 0.877043}
      scale: {x: 1.0000001, y: 1, z: 1.0000004}
    - name: VBOT_:LeftToeBase
      parentName: 
      position: {x: -0.00000046880447, y: 16.945314, z: -0.00000080466265}
      rotation: {x: 0.30737466, y: 0.05506234, z: -0.01781606, w: 0.94982713}
      scale: {x: 0.99999976, y: 0.9999996, z: 0.9999995}
    - name: VBOT_:LeftToe_End
      parentName: 
      position: {x: -0.000001521781, y: 6.2084913, z: 0.00000005960464}
      rotation: {x: -0.000000012223608, y: -0.000004991947, z: 0.00000046117927, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: VBOT_:LeftToe_End_end
      parentName: 
      position: {x: -0, y: 6.20849, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:RightUpLeg
      parentName: 
      position: {x: 10.920648, y: -5.3976483, z: 1.6830438}
      rotation: {x: 0.00000074489566, y: 0.006958819, z: 0.99995744, w: -0.006058274}
      scale: {x: 1.0005851, y: 0.99999964, z: 1.0000213}
    - name: VBOT_:RightLeg
      parentName: 
      position: {x: 0.00000036699382, y: 46.442978, z: 0.00000006676418}
      rotation: {x: -0.045270246, y: 0.00038048133, z: -0.012654801, w: 0.9988946}
      scale: {x: 0.99999946, y: 1.0000006, z: 0.99999976}
    - name: VBOT_:RightFoot
      parentName: 
      position: {x: 0.0000007188318, y: 42.515278, z: -0.0000045327465}
      rotation: {x: 0.48199156, y: 0.0065317303, z: 0.004921196, w: 0.8761377}
      scale: {x: 1.0000006, y: 1.0000002, z: 0.9999993}
    - name: VBOT_:RightToeBase
      parentName: 
      position: {x: 0.000000012107193, y: 17.102888, z: 0.0000004842877}
      rotation: {x: 0.30412865, y: -0.055317834, z: 0.017672166, w: 0.9508593}
      scale: {x: 0.9999993, y: 1.0000002, z: 0.9999997}
    - name: VBOT_:RightToe_End
      parentName: 
      position: {x: 0.000000996515, y: 6.241361, z: -0.00000017881392}
      rotation: {x: -0.000000027357602, y: 0.000034474506, z: -0.0000000888831, w: 1}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: VBOT_:RightToe_End_end
      parentName: 
      position: {x: -0, y: 6.2414274, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: VBOT_:VBOT_LOD0
      parentName: 
      position: {x: -0.000000008568167, y: 0.000000117856246, z: -0.0000000011920734}
      rotation: {x: 0.00000008122751, y: -0.0000000018335409, z: 1.1508664e-24, w: 1}
      scale: {x: 0.01, y: 0.009999999, z: 0.01}
    - name: VBOT_:VBOT_LOD1
      parentName: 
      position: {x: -0.000000008568167, y: 0.000000117856246, z: -0.0000000011920734}
      rotation: {x: 0.00000008122751, y: -0.0000000018335409, z: 1.1508664e-24, w: 1}
      scale: {x: 0.01, y: 0.009999999, z: 0.01}
    - name: VBOT_:VBOT_LOD2
      parentName: 
      position: {x: -0.000000008568167, y: 0.000000117856246, z: -0.0000000011920734}
      rotation: {x: 0.00000008122751, y: -0.0000000018335409, z: 1.1508664e-24, w: 1}
      scale: {x: 0.01, y: 0.009999999, z: 0.01}
    - name: VBOT_:VBOT_LOD3
      parentName: 
      position: {x: -0.000000008568167, y: 0.000000117856246, z: -0.0000000011920734}
      rotation: {x: 0.00000008122751, y: -0.0000000018335409, z: 1.1508664e-24, w: 1}
      scale: {x: 0.01, y: 0.009999999, z: 0.01}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
